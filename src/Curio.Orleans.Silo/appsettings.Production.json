{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Orleans": "Warning", "Microsoft.Orleans": "Error", "Curio.Infrastructure.Services": "Information"}}, "Application": {"Name": "Curio Orleans Silo", "Version": "1.0.0", "Environment": "Production"}, "Database": {"ConnectionString": "", "Host": "localhost", "Port": 5432, "Database": "orleansdb", "Username": "orleans", "Password": "", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Orleans": {"ClusterId": "curio-cluster", "ServiceId": "curio-service", "Clustering": {"Provider": "AdoNet", "ConnectionString": "", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": ""}, "Reminders": {"Provider": "AdoNet", "ConnectionString": ""}}, "Kafka": {"BrokerList": ["localhost:9092"], "ConsumerGroupId": "orleans-event-streams", "Topics": ["domain-events", "verification-events", "user-events"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500}, "Email": {"Smtp": {"Host": "", "Port": 587, "Username": "", "Password": "", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 60}, "DefaultSender": {"FromEmail": "", "FromName": "<PERSON><PERSON><PERSON>", "ReplyToEmail": "", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}}