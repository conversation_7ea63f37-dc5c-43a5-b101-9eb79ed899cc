{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Application": {"Name": "Curio API", "Version": "1.0.0", "Environment": "Production", "Api": {"BaseUrl": "", "AllowedHosts": ["*"], "RequestTimeoutSeconds": 30, "MaxRequestBodySize": 10485760, "Cors": {"Enabled": true, "AllowedOrigins": ["*"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "AllowedHeaders": ["*"], "AllowCredentials": true}}, "Security": {"Jwt": {"SecretKey": "", "Issuer": "<PERSON><PERSON><PERSON>", "Audience": "Curio.Api", "ExpirationMinutes": 60, "RefreshTokenExpirationDays": 7}, "Encryption": {"Key": "", "Salt": ""}}}, "Database": {"ConnectionString": "", "Host": "localhost", "Port": 5432, "Database": "orleansdb", "Username": "orleans", "Password": "", "CommandTimeout": 30, "MaxRetryCount": 3, "EnableSensitiveDataLogging": false}, "Orleans": {"ClusterId": "curio-cluster", "ServiceId": "curio-service", "Clustering": {"Provider": "AdoNet", "ConnectionString": "", "RefreshPeriod": 30, "DeathVoteExpirationTimeout": 120}, "Storage": {"DefaultProvider": "AdoNet", "ConnectionString": "", "UseJsonFormat": true}, "Streaming": {"Provider": "Kafka", "ConnectionString": ""}, "Reminders": {"Provider": "AdoNet", "ConnectionString": ""}}, "Kafka": {"BrokerList": ["localhost:9092"], "ConsumerGroupId": "orleans-event-streams", "Topics": ["domain-events", "verification-events", "user-events"], "SessionTimeoutMs": 30000, "HeartbeatIntervalMs": 3000, "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "AutoCommitIntervalMs": 5000, "MaxPollRecords": 500, "FetchMinBytes": 1, "FetchMaxWaitMs": 500}, "Email": {"Smtp": {"Host": "", "Port": 587, "Username": "", "Password": "", "EnableSsl": true, "TimeoutSeconds": 30, "UseDefaultCredentials": false}, "Templates": {"TemplatesDirectory": "EmailTemplates", "EnableCaching": true, "CacheExpirationMinutes": 60}, "DefaultSender": {"FromEmail": "", "FromName": "<PERSON><PERSON><PERSON>", "ReplyToEmail": "", "ReplyToName": "Curio Support"}, "Retry": {"MaxAttempts": 3, "DelayMilliseconds": 1000, "ExponentialBackoff": true}}}